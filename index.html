<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>空战游戏 - Air Combat Game</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                overflow: hidden;
                background: #000;
                font-family: Arial, sans-serif;
            }

            #gameContainer {
                position: relative;
                width: 100vw;
                height: 100vh;
            }

            #gameCanvas {
                display: block;
            }

            #hud {
                position: absolute;
                top: 20px;
                left: 20px;
                color: white;
                font-size: 18px;
                z-index: 100;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }

            #controls {
                position: absolute;
                bottom: 20px;
                left: 20px;
                color: white;
                font-size: 14px;
                z-index: 100;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }

            #gameOver {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                text-align: center;
                z-index: 200;
                display: none;
                background: rgba(0, 0, 0, 0.8);
                padding: 30px;
                border-radius: 10px;
            }

            #startButton {
                background: #4caf50;
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 18px;
                cursor: pointer;
                border-radius: 5px;
                margin-top: 20px;
            }

            #startButton:hover {
                background: #45a049;
            }

            .crosshair {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                border: 2px solid rgba(255, 255, 255, 0.8);
                border-radius: 50%;
                z-index: 50;
                pointer-events: none;
            }

            .crosshair::before {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
            }

            #radar {
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.7);
                border: 2px solid #00ff00;
                border-radius: 10px;
                padding: 10px;
                color: #00ff00;
                font-size: 12px;
            }

            #radarTitle {
                text-align: center;
                margin-bottom: 5px;
                font-weight: bold;
            }

            #radarCanvas {
                background: rgba(0, 50, 0, 0.3);
                border: 1px solid #00ff00;
                border-radius: 50%;
            }

            #ammo {
                position: absolute;
                bottom: 20px;
                right: 20px;
                color: white;
                font-size: 14px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }

            .health-bar {
                position: absolute;
                bottom: 80px;
                left: 20px;
                width: 200px;
                height: 20px;
                background: rgba(255, 0, 0, 0.3);
                border: 2px solid white;
                border-radius: 10px;
            }

            .health-fill {
                height: 100%;
                background: linear-gradient(90deg, #ff0000, #ffff00, #00ff00);
                border-radius: 8px;
                transition: width 0.3s ease;
            }
        </style>
    </head>
    <body>
        <div id="gameContainer">
            <canvas id="gameCanvas"></canvas>

            <!-- 准星 -->
            <div class="crosshair"></div>

            <!-- HUD界面 -->
            <div id="hud">
                <div>分数: <span id="score">0</span></div>
                <div>生命: <span id="health">100</span></div>
                <div>敌机: <span id="enemies">0</span></div>
                <div>状态: <span id="gameStatus">正在加载...</span></div>
            </div>

            <!-- 生命值条 -->
            <div class="health-bar">
                <div
                    class="health-fill"
                    id="healthBar"
                    style="width: 100%"
                ></div>
            </div>

            <!-- 控制说明 -->
            <div id="controls">
                <div><strong>游戏控制:</strong></div>
                <div>↑↓←→ 或 WASD - 飞机移动</div>
                <div>空格键 - 发射炮弹</div>
                <div>Shift - 加速推进</div>
                <div>ESC - 暂停/继续</div>
                <div style="margin-top: 10px; font-size: 12px; opacity: 0.8">
                    击毁敌机获得分数，避免被敌机击中！
                </div>
            </div>

            <!-- 雷达显示 -->
            <div id="radar">
                <div id="radarTitle">雷达</div>
                <canvas id="radarCanvas" width="120" height="120"></canvas>
            </div>

            <!-- 弹药显示 -->
            <div id="ammo">
                <div>弹药: <span id="ammoCount">∞</span></div>
                <div>温度: <span id="weaponTemp">正常</span></div>
            </div>

            <!-- 暂停界面 -->
            <div
                id="pauseScreen"
                style="
                    display: none;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 30px;
                    border-radius: 10px;
                    text-align: center;
                    z-index: 200;
                "
            >
                <h2>游戏暂停</h2>
                <p>按 ESC 键继续游戏</p>
            </div>

            <!-- 游戏结束界面 -->
            <div id="gameOver">
                <h2>游戏结束</h2>
                <p>最终分数: <span id="finalScore">0</span></p>
                <button id="startButton" onclick="restartGame()">
                    重新开始
                </button>
            </div>
        </div>

        <!-- Three.js 库 -->
        <script src="three.min.js"></script>
        <script>
            // 检查Three.js是否正确加载
            window.addEventListener("load", function () {
                console.log("页面加载完成");
                console.log(
                    "THREE.js 状态:",
                    typeof THREE !== "undefined" ? "已加载" : "未加载"
                );
                if (typeof THREE !== "undefined") {
                    console.log("THREE.js 版本:", THREE.REVISION);
                }
            });
        </script>

        <!-- 游戏脚本 -->
        <script src="js/game.js"></script>
    </body>
</html>
