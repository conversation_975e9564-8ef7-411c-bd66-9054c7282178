<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单控制测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #222;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #player {
            position: absolute;
            width: 50px;
            height: 50px;
            background: #00ff00;
            border-radius: 50%;
            top: 200px;
            left: 200px;
            transition: all 0.1s;
        }
        
        #info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>使用 WASD 或方向键移动绿色圆圈</div>
        <div>空格键改变颜色</div>
        <div id="position">位置: (200, 200)</div>
        <div id="keys">按键: 无</div>
    </div>
    
    <div id="player"></div>

    <script>
        let keys = {};
        let player = document.getElementById('player');
        let x = 200, y = 200;
        
        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                console.log('按键按下:', event.code);
                updateKeysDisplay();
                event.preventDefault();
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
                updateKeysDisplay();
                event.preventDefault();
            });
        }
        
        function updateKeysDisplay() {
            const activeKeys = Object.keys(keys).filter(key => keys[key]);
            document.getElementById('keys').textContent = '按键: ' + (activeKeys.length > 0 ? activeKeys.join(', ') : '无');
        }
        
        function updatePlayer() {
            const speed = 5;
            let moved = false;
            
            if (keys['ArrowLeft'] || keys['KeyA']) {
                x -= speed;
                moved = true;
            }
            if (keys['ArrowRight'] || keys['KeyD']) {
                x += speed;
                moved = true;
            }
            if (keys['ArrowUp'] || keys['KeyW']) {
                y -= speed;
                moved = true;
            }
            if (keys['ArrowDown'] || keys['KeyS']) {
                y += speed;
                moved = true;
            }
            if (keys['Space']) {
                const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                player.style.background = colors[Math.floor(Math.random() * colors.length)];
            }
            
            if (moved) {
                player.style.left = x + 'px';
                player.style.top = y + 'px';
                document.getElementById('position').textContent = `位置: (${x}, ${y})`;
            }
        }
        
        function gameLoop() {
            updatePlayer();
            requestAnimationFrame(gameLoop);
        }
        
        // 初始化
        setupEventListeners();
        gameLoop();
        
        console.log('简单测试初始化完成');
    </script>
</body>
</html>
