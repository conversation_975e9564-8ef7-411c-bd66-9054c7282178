<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>使用 WASD 或方向键移动立方体</div>
        <div>空格键改变颜色</div>
        <div id="position">位置: (0, 0, 0)</div>
        <div id="keys">按键: 无</div>
    </div>

    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <script>
        // 简单的Three.js测试
        let scene, camera, renderer, cube;
        let keys = {};
        
        function init() {
            console.log('开始初始化...');
            console.log('THREE.js 是否加载:', typeof THREE !== 'undefined');
            
            if (typeof THREE === 'undefined') {
                alert('THREE.js 未加载！');
                return;
            }
            
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer();
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            
            // 创建立方体
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            cube = new THREE.Mesh(geometry, material);
            scene.add(cube);
            
            // 添加光源
            const light = new THREE.DirectionalLight(0xffffff, 1);
            light.position.set(1, 1, 1);
            scene.add(light);
            
            // 设置事件监听
            setupEventListeners();
            
            // 开始动画循环
            animate();
            
            console.log('初始化完成');
        }
        
        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                console.log('按键按下:', event.code);
                updateKeysDisplay();
                event.preventDefault();
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
                updateKeysDisplay();
                event.preventDefault();
            });
        }
        
        function updateKeysDisplay() {
            const activeKeys = Object.keys(keys).filter(key => keys[key]);
            document.getElementById('keys').textContent = '按键: ' + (activeKeys.length > 0 ? activeKeys.join(', ') : '无');
        }
        
        function updateCube() {
            const speed = 0.1;
            let moved = false;
            
            if (keys['ArrowLeft'] || keys['KeyA']) {
                cube.position.x -= speed;
                moved = true;
            }
            if (keys['ArrowRight'] || keys['KeyD']) {
                cube.position.x += speed;
                moved = true;
            }
            if (keys['ArrowUp'] || keys['KeyW']) {
                cube.position.y += speed;
                moved = true;
            }
            if (keys['ArrowDown'] || keys['KeyS']) {
                cube.position.y -= speed;
                moved = true;
            }
            if (keys['Space']) {
                cube.material.color.setHex(Math.random() * 0xffffff);
            }
            
            if (moved) {
                document.getElementById('position').textContent = 
                    `位置: (${cube.position.x.toFixed(1)}, ${cube.position.y.toFixed(1)}, ${cube.position.z.toFixed(1)})`;
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            updateCube();
            cube.rotation.x += 0.01;
            cube.rotation.y += 0.01;
            
            renderer.render(scene, camera);
        }
        
        // 页面加载完成后启动
        window.addEventListener('load', init);
    </script>
</body>
</html>
