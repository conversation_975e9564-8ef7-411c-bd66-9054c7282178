// 空战游戏主文件
class AirCombatGame {
    constructor() {
        // 游戏状态
        this.gameState = "playing"; // 'playing', 'paused', 'gameOver'
        this.score = 0;
        this.health = 100;
        this.enemyCount = 0;

        // Three.js 核心对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();

        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.particles = [];

        // 输入控制
        this.keys = {};
        this.mouse = { x: 0, y: 0 };

        // 游戏设置
        this.settings = {
            playerSpeed: 50,
            bulletSpeed: 100,
            enemySpeed: 30,
            maxBullets: 50,
            maxEnemies: 10,
            spawnRate: 0.02,
            difficulty: 1,
        };

        // 性能监控
        this.frameCount = 0;
        this.lastFPSUpdate = 0;

        this.init();
    }

    init() {
        this.setupRenderer();
        this.setupScene();
        this.setupCamera();
        this.setupLights();
        this.setupEventListeners();
        this.createPlayer();
        this.setupRadar();
        this.animate();

        console.log("空战游戏初始化完成");
        console.log("玩家飞机位置:", this.player.position);
        console.log("游戏状态:", this.gameState);
    }

    setupRenderer() {
        const canvas = document.getElementById("gameCanvas");
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.setClearColor(0x87ceeb, 1); // 天空蓝色
    }

    setupScene() {
        this.scene = new THREE.Scene();

        // 添加雾效果
        this.scene.fog = new THREE.Fog(0x87ceeb, 100, 2000);

        // 创建天空盒
        this.createSkybox();

        // 创建地面
        this.createGround();

        // 创建云朵
        this.createClouds();
    }

    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            3000
        );
        this.camera.position.set(0, 10, 20);
    }

    setupLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // 方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
    }

    setupEventListeners() {
        // 键盘事件
        document.addEventListener("keydown", (event) => {
            this.keys[event.code] = true;
            console.log(`按键按下: ${event.code}`);
            // 阻止默认行为，特别是方向键
            if (
                [
                    "ArrowUp",
                    "ArrowDown",
                    "ArrowLeft",
                    "ArrowRight",
                    "Space",
                ].includes(event.code)
            ) {
                event.preventDefault();
            }
        });

        document.addEventListener("keyup", (event) => {
            this.keys[event.code] = false;
            // 阻止默认行为
            if (
                [
                    "ArrowUp",
                    "ArrowDown",
                    "ArrowLeft",
                    "ArrowRight",
                    "Space",
                ].includes(event.code)
            ) {
                event.preventDefault();
            }
        });

        // 鼠标事件
        document.addEventListener("mousemove", (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        // 窗口大小调整
        window.addEventListener("resize", () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 阻止右键菜单
        document.addEventListener("contextmenu", (event) => {
            event.preventDefault();
        });

        // ESC键暂停游戏
        document.addEventListener("keydown", (event) => {
            if (event.code === "Escape") {
                this.togglePause();
            }
        });
    }

    setupRadar() {
        this.radarCanvas = document.getElementById("radarCanvas");
        this.radarCtx = this.radarCanvas.getContext("2d");
    }

    togglePause() {
        if (this.gameState === "playing") {
            this.gameState = "paused";
        } else if (this.gameState === "paused") {
            this.gameState = "playing";
        }
    }

    createPlayer() {
        // 创建玩家飞机组
        this.player = new THREE.Group();
        this.player.position.set(0, 20, 0);

        // 机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.8, 5, 8);
        const fuselageMaterial = new THREE.MeshLambertMaterial({
            color: 0x0066cc,
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        this.player.add(fuselage);

        // 机头
        const noseGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
        const noseMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 });
        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.position.set(3.25, 0, 0);
        nose.rotation.z = Math.PI / 2;
        nose.castShadow = true;
        this.player.add(nose);

        // 主翼
        const mainWingGeometry = new THREE.BoxGeometry(0.3, 8, 1.5);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 });
        const mainWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
        mainWing.position.set(-0.5, 0, 0);
        mainWing.castShadow = true;
        this.player.add(mainWing);

        // 尾翼
        const tailWingGeometry = new THREE.BoxGeometry(0.2, 3, 1);
        const tailWing = new THREE.Mesh(tailWingGeometry, wingMaterial);
        tailWing.position.set(-2, 0, 0);
        tailWing.castShadow = true;
        this.player.add(tailWing);

        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.2, 1, 2);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
        verticalTail.position.set(-2, 0, 1);
        verticalTail.castShadow = true;
        this.player.add(verticalTail);

        // 引擎喷射效果（初始隐藏）
        const thrustGeometry = new THREE.ConeGeometry(0.2, 1, 6);
        const thrustMaterial = new THREE.MeshBasicMaterial({
            color: 0xff4400,
            transparent: true,
            opacity: 0.8,
        });
        this.playerThrust = new THREE.Mesh(thrustGeometry, thrustMaterial);
        this.playerThrust.position.set(-3, 0, 0);
        this.playerThrust.rotation.z = -Math.PI / 2;
        this.playerThrust.visible = false;
        this.player.add(this.playerThrust);

        // 玩家状态（简化）
        this.player.userData = {
            health: 100,
        };

        this.scene.add(this.player);
    }

    createSkybox() {
        // 创建天空盒几何体
        const skyGeometry = new THREE.SphereGeometry(1000, 32, 32);

        // 创建渐变天空材质
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0x87ceeb) },
                offset: { value: 33 },
                exponent: { value: 0.6 },
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide,
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    createGround() {
        // 创建海洋地面
        const groundGeometry = new THREE.PlaneGeometry(2000, 2000, 100, 100);

        // 创建水面材质
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x006994,
            transparent: true,
            opacity: 0.8,
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -50;
        ground.receiveShadow = true;

        // 添加水面波动效果
        ground.userData = {
            originalVertices: groundGeometry.attributes.position.array.slice(),
        };
        this.ground = ground;

        this.scene.add(ground);
    }

    createClouds() {
        this.clouds = [];

        for (let i = 0; i < 20; i++) {
            const cloudGeometry = new THREE.SphereGeometry(
                Math.random() * 10 + 5,
                8,
                8
            );

            const cloudMaterial = new THREE.MeshLambertMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.7,
            });

            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);

            // 随机位置
            cloud.position.set(
                (Math.random() - 0.5) * 1000,
                Math.random() * 100 + 50,
                (Math.random() - 0.5) * 1000
            );

            // 随机缩放
            const scale = Math.random() * 2 + 0.5;
            cloud.scale.set(scale, scale * 0.5, scale);

            this.clouds.push(cloud);
            this.scene.add(cloud);
        }
    }

    updatePlayer(deltaTime) {
        if (!this.player || this.gameState !== "playing") {
            console.log(
                "updatePlayer 跳过:",
                !this.player ? "没有玩家" : "游戏状态:" + this.gameState
            );
            return;
        }

        const speed = this.settings.playerSpeed * deltaTime;
        const isBoost = this.keys["ShiftLeft"] || this.keys["ShiftRight"];
        const boostMultiplier = isBoost ? 2 : 1;
        const finalSpeed = speed * boostMultiplier;

        // 调试：检查按键状态
        let moved = false;

        // 检查是否有任何按键被按下
        const activeKeys = Object.keys(this.keys).filter(
            (key) => this.keys[key]
        );
        if (activeKeys.length > 0) {
            console.log("当前按下的键:", activeKeys);
        }

        // 显示/隐藏引擎喷射效果
        if (this.playerThrust) {
            this.playerThrust.visible = isBoost;
        }

        // 上下左右直接控制位置
        if (this.keys["ArrowLeft"] || this.keys["KeyA"]) {
            this.player.position.x -= finalSpeed;
            moved = true;
            // 添加倾斜效果
            this.player.rotation.z = Math.min(
                this.player.rotation.z + deltaTime * 2,
                0.3
            );
        } else if (this.keys["ArrowRight"] || this.keys["KeyD"]) {
            this.player.position.x += finalSpeed;
            moved = true;
            // 添加倾斜效果
            this.player.rotation.z = Math.max(
                this.player.rotation.z - deltaTime * 2,
                -0.3
            );
        } else {
            // 自动回正倾斜角度
            this.player.rotation.z *= 0.9;
        }

        if (this.keys["ArrowUp"] || this.keys["KeyW"]) {
            this.player.position.y += finalSpeed;
            moved = true;
            // 添加俯仰效果
            this.player.rotation.x = Math.max(
                this.player.rotation.x - deltaTime * 1,
                -0.2
            );
        } else if (this.keys["ArrowDown"] || this.keys["KeyS"]) {
            this.player.position.y -= finalSpeed;
            moved = true;
            // 添加俯仰效果
            this.player.rotation.x = Math.min(
                this.player.rotation.x + deltaTime * 1,
                0.2
            );
        } else {
            // 自动回正俯仰角度
            this.player.rotation.x *= 0.9;
        }

        // 调试信息
        if (moved) {
            console.log(
                `飞机位置: x=${this.player.position.x.toFixed(
                    1
                )}, y=${this.player.position.y.toFixed(
                    1
                )}, z=${this.player.position.z.toFixed(1)}`
            );
        }

        // 限制移动范围
        this.player.position.x = Math.max(
            -80,
            Math.min(80, this.player.position.x)
        );
        this.player.position.y = Math.max(
            5,
            Math.min(60, this.player.position.y)
        );
        this.player.position.z = Math.max(
            -80,
            Math.min(80, this.player.position.z)
        );

        // 空格发射炮弹
        if (this.keys["Space"]) {
            this.shootBullet();
        }

        // 更新相机跟随
        this.updateCamera();
    }

    updateCamera() {
        if (!this.player) return;

        // 简化的第三人称跟随相机
        const offset = new THREE.Vector3(0, 10, 20);
        const targetPosition = this.player.position.clone().add(offset);

        // 平滑相机移动
        this.camera.position.lerp(targetPosition, 0.1);
        this.camera.lookAt(this.player.position);
    }

    shootBullet() {
        // 限制射击频率
        const now = Date.now();
        if (this.lastShot && now - this.lastShot < 150) return;
        this.lastShot = now;

        if (this.bullets.length >= this.settings.maxBullets) return;

        // 创建双炮弹（左右各一发）
        const positions = [
            new THREE.Vector3(-1, 0, 2), // 左炮口
            new THREE.Vector3(1, 0, 2), // 右炮口
        ];

        positions.forEach((offset) => {
            const bullet = this.createBullet(offset);
            this.bullets.push(bullet);
            this.scene.add(bullet);
        });
    }

    createBullet(offset) {
        // 创建炮弹几何体
        const bulletGeometry = new THREE.CylinderGeometry(0.05, 0.1, 0.5, 6);
        const bulletMaterial = new THREE.MeshBasicMaterial({
            color: 0xffaa00,
            emissive: 0xff4400,
        });
        const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

        // 设置炮弹位置
        const bulletPosition = offset.clone();
        bulletPosition.applyQuaternion(this.player.quaternion);
        bullet.position.copy(this.player.position).add(bulletPosition);

        // 设置炮弹方向
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyQuaternion(this.player.quaternion);

        // 炮弹旋转对齐方向
        bullet.lookAt(bullet.position.clone().add(direction));
        bullet.rotateX(Math.PI / 2);

        // 创建炮弹轨迹
        const trailGeometry = new THREE.BufferGeometry();
        const trailMaterial = new THREE.LineBasicMaterial({
            color: 0xffaa00,
            transparent: true,
            opacity: 0.6,
        });
        const trail = new THREE.Line(trailGeometry, trailMaterial);

        bullet.userData = {
            direction: direction,
            life: 4.0,
            speed: this.settings.bulletSpeed,
            trail: trail,
            trailPositions: [],
        };

        this.scene.add(trail);
        return bullet;
    }

    updateBullets(deltaTime) {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            const userData = bullet.userData;

            // 移动炮弹
            const moveDistance = userData.speed * deltaTime;
            bullet.position.add(
                userData.direction.clone().multiplyScalar(moveDistance)
            );

            // 炮弹旋转效果
            bullet.rotation.z += deltaTime * 10;

            // 更新轨迹
            this.updateBulletTrail(bullet);

            // 减少生命值
            userData.life -= deltaTime;

            // 移除过期炮弹
            if (userData.life <= 0) {
                this.removeBullet(i);
            }
        }
    }

    updateBulletTrail(bullet) {
        const userData = bullet.userData;
        const trail = userData.trail;

        // 添加当前位置到轨迹
        userData.trailPositions.push(bullet.position.clone());

        // 限制轨迹长度
        const maxTrailLength = 10;
        if (userData.trailPositions.length > maxTrailLength) {
            userData.trailPositions.shift();
        }

        // 更新轨迹几何体
        if (userData.trailPositions.length > 1) {
            const positions = [];
            userData.trailPositions.forEach((pos) => {
                positions.push(pos.x, pos.y, pos.z);
            });

            trail.geometry.setAttribute(
                "position",
                new THREE.Float32BufferAttribute(positions, 3)
            );
            trail.geometry.attributes.position.needsUpdate = true;
        }
    }

    removeBullet(index) {
        const bullet = this.bullets[index];
        if (bullet.userData.trail) {
            this.scene.remove(bullet.userData.trail);
        }
        this.scene.remove(bullet);
        this.bullets.splice(index, 1);
    }

    spawnEnemies() {
        // 根据难度调整生成频率
        const adjustedSpawnRate =
            this.settings.spawnRate * this.settings.difficulty;
        const maxEnemies = Math.min(
            this.settings.maxEnemies + Math.floor(this.settings.difficulty / 2),
            20
        );

        // 控制敌机生成频率
        if (
            Math.random() < adjustedSpawnRate &&
            this.enemies.length < maxEnemies
        ) {
            this.createEnemy();
        }

        // 每1000分增加难度
        const newDifficulty = 1 + Math.floor(this.score / 1000);
        if (newDifficulty > this.settings.difficulty) {
            this.settings.difficulty = newDifficulty;
            console.log(`难度提升到: ${this.settings.difficulty}`);
        }
    }

    createEnemy() {
        // 创建敌机组
        const enemy = new THREE.Group();

        // 敌机机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.2, 0.5, 3, 6);
        const fuselageMaterial = new THREE.MeshLambertMaterial({
            color: 0xcc0000,
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        enemy.add(fuselage);

        // 敌机机翼
        const wingGeometry = new THREE.BoxGeometry(0.2, 4, 0.8);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x990000 });
        const wing = new THREE.Mesh(wingGeometry, wingMaterial);
        wing.position.set(-0.3, 0, 0);
        wing.castShadow = true;
        enemy.add(wing);

        // 随机生成位置
        const spawnDistance = 150;
        const angle = Math.random() * Math.PI * 2;
        const x = Math.cos(angle) * spawnDistance;
        const z = Math.sin(angle) * spawnDistance;
        const y = Math.random() * 40 + 20;

        enemy.position.set(x, y, z);

        // 敌机AI数据（根据难度调整）
        enemy.userData = {
            health: 2 + Math.floor(this.settings.difficulty / 2),
            speed:
                this.settings.enemySpeed * (1 + this.settings.difficulty * 0.1),
            aiType: Math.random() < 0.5 ? "chase" : "patrol",
            patrolTarget: new THREE.Vector3(
                (Math.random() - 0.5) * 100,
                Math.random() * 60 + 10,
                (Math.random() - 0.5) * 100
            ),
            lastShot: 0,
            shootCooldown:
                Math.max(1000, 2000 - this.settings.difficulty * 100) +
                Math.random() * 1000,
        };

        this.enemies.push(enemy);
        this.scene.add(enemy);
    }

    updateEnemies(deltaTime) {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            const userData = enemy.userData;

            // 敌机AI行为
            this.updateEnemyAI(enemy, deltaTime);

            // 移除距离过远的敌机
            const distanceToPlayer = enemy.position.distanceTo(
                this.player.position
            );
            if (distanceToPlayer > 300) {
                this.removeEnemy(i);
            }
        }
    }

    updateEnemyAI(enemy, deltaTime) {
        const userData = enemy.userData;
        const speed = userData.speed * deltaTime;

        if (userData.aiType === "chase") {
            // 追击模式：朝玩家飞行
            const direction = new THREE.Vector3()
                .subVectors(this.player.position, enemy.position)
                .normalize();

            enemy.position.add(direction.multiplyScalar(speed));
            enemy.lookAt(this.player.position);
        } else if (userData.aiType === "patrol") {
            // 巡逻模式：在目标点之间移动
            const direction = new THREE.Vector3()
                .subVectors(userData.patrolTarget, enemy.position)
                .normalize();

            enemy.position.add(direction.multiplyScalar(speed));
            enemy.lookAt(userData.patrolTarget);

            // 到达巡逻点后选择新目标
            if (enemy.position.distanceTo(userData.patrolTarget) < 5) {
                userData.patrolTarget.set(
                    (Math.random() - 0.5) * 100,
                    Math.random() * 60 + 10,
                    (Math.random() - 0.5) * 100
                );
            }
        }

        // 敌机射击（简单实现）
        const now = Date.now();
        const distanceToPlayer = enemy.position.distanceTo(
            this.player.position
        );
        if (
            distanceToPlayer < 50 &&
            now - userData.lastShot > userData.shootCooldown
        ) {
            this.createEnemyBullet(enemy);
            userData.lastShot = now;
        }
    }

    createEnemyBullet(enemy) {
        // 创建敌机炮弹
        const bulletGeometry = new THREE.SphereGeometry(0.1, 6, 6);
        const bulletMaterial = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            emissive: 0x440000,
        });
        const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

        bullet.position.copy(enemy.position);

        const direction = new THREE.Vector3()
            .subVectors(this.player.position, enemy.position)
            .normalize();

        bullet.userData = {
            direction: direction,
            life: 3.0,
            speed: 80,
            isEnemyBullet: true,
        };

        this.bullets.push(bullet);
        this.scene.add(bullet);
    }

    removeEnemy(index) {
        const enemy = this.enemies[index];
        this.scene.remove(enemy);
        this.enemies.splice(index, 1);
    }

    checkCollisions() {
        // 检测玩家炮弹与敌机的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            // 跳过敌机炮弹
            if (bullet.userData.isEnemyBullet) {
                // 检测敌机炮弹与玩家的碰撞
                this.checkEnemyBulletPlayerCollision(bullet, i);
                continue;
            }

            // 检测玩家炮弹与敌机的碰撞
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];
                const distance = bullet.position.distanceTo(enemy.position);

                if (distance < 2) {
                    // 碰撞发生
                    this.handleEnemyHit(enemy, j, bullet, i);
                    break; // 炮弹已被移除，跳出循环
                }
            }
        }
    }

    checkEnemyBulletPlayerCollision(bullet, bulletIndex) {
        const distance = bullet.position.distanceTo(this.player.position);

        if (distance < 3) {
            // 玩家被击中
            this.health -= 10;
            this.removeBullet(bulletIndex);

            // 创建击中效果
            this.createHitEffect(this.player.position);

            // 检查游戏结束
            if (this.health <= 0) {
                this.gameOver();
            }
        }
    }

    handleEnemyHit(enemy, enemyIndex, bullet, bulletIndex) {
        // 减少敌机生命值
        enemy.userData.health -= 1;

        // 创建击中效果
        this.createHitEffect(enemy.position);

        // 移除炮弹
        this.removeBullet(bulletIndex);

        if (enemy.userData.health <= 0) {
            // 敌机被摧毁
            this.createExplosion(enemy.position);
            this.removeEnemy(enemyIndex);
            this.score += 100;
        }
    }

    createHitEffect(position) {
        // 创建击中火花效果
        const sparkCount = 8;
        for (let i = 0; i < sparkCount; i++) {
            const sparkGeometry = new THREE.SphereGeometry(0.05, 4, 4);
            const sparkMaterial = new THREE.MeshBasicMaterial({
                color: 0xffaa00,
                emissive: 0xff4400,
            });
            const spark = new THREE.Mesh(sparkGeometry, sparkMaterial);

            spark.position.copy(position);

            // 随机方向
            const direction = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize();

            spark.userData = {
                velocity: direction.multiplyScalar(20),
                life: 0.5,
            };

            this.particles.push(spark);
            this.scene.add(spark);
        }
    }

    createExplosion(position) {
        // 创建爆炸效果
        const particleCount = 15;
        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.1, 6, 6);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(Math.random() * 0.1, 1, 0.5),
                emissive: new THREE.Color().setHSL(Math.random() * 0.1, 1, 0.3),
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            particle.position.copy(position);

            // 随机爆炸方向
            const direction = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize();

            particle.userData = {
                velocity: direction.multiplyScalar(30 + Math.random() * 20),
                life: 1.0 + Math.random() * 0.5,
                gravity: -20,
            };

            this.particles.push(particle);
            this.scene.add(particle);
        }
    }

    updateEnvironment(deltaTime) {
        // 更新云朵移动
        if (this.clouds) {
            this.clouds.forEach((cloud) => {
                cloud.position.x += deltaTime * 5;
                if (cloud.position.x > 500) {
                    cloud.position.x = -500;
                }
            });
        }

        // 更新水面波动
        if (this.ground) {
            const time = this.clock.getElapsedTime();
            const vertices = this.ground.geometry.attributes.position.array;
            const originalVertices = this.ground.userData.originalVertices;

            for (let i = 0; i < vertices.length; i += 3) {
                const x = originalVertices[i];
                const z = originalVertices[i + 2];
                vertices[i + 1] =
                    originalVertices[i + 1] +
                    Math.sin(time * 2 + x * 0.01) * 0.5 +
                    Math.cos(time * 1.5 + z * 0.01) * 0.3;
            }

            this.ground.geometry.attributes.position.needsUpdate = true;
        }
    }

    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            const userData = particle.userData;

            // 更新粒子位置
            particle.position.add(
                userData.velocity.clone().multiplyScalar(deltaTime)
            );

            // 应用重力
            if (userData.gravity) {
                userData.velocity.y += userData.gravity * deltaTime;
            }

            // 减少生命值
            userData.life -= deltaTime;

            // 淡出效果
            if (particle.material) {
                particle.material.opacity = userData.life / 1.0;
            }

            // 移除过期粒子
            if (userData.life <= 0) {
                this.scene.remove(particle);
                this.particles.splice(i, 1);
            }
        }
    }

    gameOver() {
        this.gameState = "gameOver";
        document.getElementById("finalScore").textContent = this.score;
        document.getElementById("gameOver").style.display = "block";
    }

    updateHUD() {
        document.getElementById("score").textContent = this.score;
        document.getElementById("health").textContent = this.health;
        document.getElementById("enemies").textContent = this.enemies.length;

        // 更新生命值条
        const healthPercent = Math.max(0, this.health) / 100;
        const healthBar = document.getElementById("healthBar");
        if (healthBar) {
            healthBar.style.width = healthPercent * 100 + "%";
        }

        // 更新雷达
        this.updateRadar();
    }

    updateRadar() {
        if (!this.radarCtx) return;

        const ctx = this.radarCtx;
        const canvas = this.radarCanvas;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = 50;

        // 清空雷达
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制雷达圆圈
        ctx.strokeStyle = "#00ff00";
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();

        // 绘制雷达扫描线
        const time = this.clock.getElapsedTime();
        const sweepAngle = (time * 2) % (Math.PI * 2);
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(
            centerX + Math.cos(sweepAngle) * radius,
            centerY + Math.sin(sweepAngle) * radius
        );
        ctx.stroke();

        // 绘制玩家位置（中心点）
        ctx.fillStyle = "#00ff00";
        ctx.beginPath();
        ctx.arc(centerX, centerY, 2, 0, Math.PI * 2);
        ctx.fill();

        // 绘制敌机位置
        ctx.fillStyle = "#ff0000";
        this.enemies.forEach((enemy) => {
            const distance = this.player.position.distanceTo(enemy.position);
            if (distance < 100) {
                // 雷达范围
                const relativePos = enemy.position
                    .clone()
                    .sub(this.player.position);
                const radarX = centerX + (relativePos.x / 100) * radius;
                const radarY = centerY + (relativePos.z / 100) * radius;

                ctx.beginPath();
                ctx.arc(radarX, radarY, 2, 0, Math.PI * 2);
                ctx.fill();
            }
        });
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        const deltaTime = this.clock.getDelta();

        // 性能监控
        this.frameCount++;
        const now = performance.now();
        if (now - this.lastFPSUpdate > 1000) {
            const fps = this.frameCount;
            this.frameCount = 0;
            this.lastFPSUpdate = now;

            // 如果FPS过低，自动降低质量
            if (fps < 30) {
                this.optimizePerformance();
            }
        }

        if (this.gameState === "playing") {
            this.updatePlayer(deltaTime);
            this.updateBullets(deltaTime);
            this.updateEnemies(deltaTime);
            this.updateParticles(deltaTime);
            this.checkCollisions();
            this.spawnEnemies();
            this.updateEnvironment(deltaTime);
            this.updateHUD();
        } else if (this.gameState === "paused") {
            // 暂停时显示提示
            this.showPauseMessage();
        }

        this.renderer.render(this.scene, this.camera);
    }

    optimizePerformance() {
        // 减少粒子数量
        if (this.particles.length > 20) {
            const toRemove = this.particles.splice(20);
            toRemove.forEach((particle) => this.scene.remove(particle));
        }

        // 减少最大敌机数量
        this.settings.maxEnemies = Math.max(5, this.settings.maxEnemies - 1);

        console.log("性能优化：减少了粒子和敌机数量");
    }

    showPauseMessage() {
        // 可以在这里添加暂停提示的逻辑
        // 暂时留空，因为我们已经有ESC键提示
    }
}

// 游戏实例
let game;

// 启动游戏
function startGame() {
    console.log("开始启动游戏...");
    console.log("THREE.js 是否加载:", typeof THREE !== "undefined");

    if (typeof THREE === "undefined") {
        console.error("THREE.js 未加载！");
        alert("THREE.js 库未正确加载，请检查网络连接！");
        return;
    }

    try {
        game = new AirCombatGame();
        console.log("游戏创建成功");
    } catch (error) {
        console.error("游戏创建失败:", error);
        alert("游戏启动失败: " + error.message);
    }
}

// 重启游戏
function restartGame() {
    if (game) {
        // 清理现有游戏资源
        game.gameState = "gameOver";

        // 清理场景中的所有对象
        while (game.scene.children.length > 0) {
            game.scene.remove(game.scene.children[0]);
        }

        // 清理数组
        game.bullets = [];
        game.enemies = [];
        game.particles = [];
        game.clouds = [];
    }

    document.getElementById("gameOver").style.display = "none";
    startGame();
}

// 页面加载完成后启动游戏
window.addEventListener("load", startGame);
